<ul role="list" class="divide-y divide-gray-200 peer" id="songs_list">
  <!-- Generation Task Placeholders (2 per task) -->
  <% @generating_tasks.each do |task| %>
    <% 2.times do |index| %>
      <%= render "generations/placeholder", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Failed Generation Tasks (2 per task) -->
  <% @failed_tasks.each do |task| %>
    <% 2.times do |index| %>
      <%= render "generations/failed_list_item", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Actual Songs -->
  <% @songs.each do |song| %>
    <%= render "song_list_item", song: song %>
  <% end %>
</ul>
<!-- 搜索无结果状态 -->
<% if @songs.empty? && @generating_tasks.empty? && @failed_tasks.empty? && (params[:search].present? || params[:favorites] == 'true') %>
  <div class="p-6 text-center flex-1 flex flex-col justify-center">
    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
      <%= flowbite_icon('search-outline', class: 'size-8 text-gray-400') %>
    </div>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No songs found</h3>
    <p class="text-gray-500 mb-4">
      <% if params[:search].present? %>
        No songs match your search for "<%= params[:search] %>"
      <% elsif params[:favorites] == 'true' %>
        You haven't favorited any songs yet
      <% end %>
    </p>
    <%= link_to "Clear filters", songs_path,
      data: { turbo_frame: "song_list_frame" },
      class: "inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100" %>
  </div>
<% else %>
  <div class="p-6 text-center flex-1 flex peer-has-[li]:hidden flex-col justify-center">
    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
      <%= flowbite_icon('music-outline', class: 'size-8 text-gray-400') %>
    </div>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No songs yet</h3>
    <p class="text-gray-500">Start creating your first AI-generated song using the generation form</p>
  </div>
<% end %>