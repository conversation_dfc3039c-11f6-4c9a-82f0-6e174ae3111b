<% if task.songs.any? %>
  <% task.songs.each_with_index do |song, index| %>
    <%# Replace placeholder with actual song list item %>
    <%= turbo_stream.replace generation_task_placeholder_id(task, index) do %>
      <%= render "songs/song_list_item", song: song %>
    <% end %>
    <%# Update song details panel %>
    <%= turbo_stream.replace dom_id(song, :song_info), method: :morph do %>
      <%= render "songs/song_info", song: song %>
    <% end %>
    <%# Update player %>
    <%= turbo_stream.replace song_stream_player_id(song), method: :morph do %>
      <%= render "songs/player", song: song %>
    <% end %>
  <% end %>
  <%# Remove any remaining placeholders if fewer than 2 songs %>
  <% if task.songs.count < 2 %>
    <% (task.songs.count...2).each do |index| %>
      <%= turbo_stream.remove generation_task_placeholder_id(task, index) %>
    <% end %>
  <% end %>
<% elsif task.failed? %>
  <% 2.times do |index| %>
    <%= turbo_stream.replace generation_task_placeholder_id(task, index) do %>
      <%= render "generations/failed_list_item", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Clean up non-list-item elements for failed tasks -->
  <%= turbo_stream.replace "song_player", method: :morph do %>
    <%= render "songs/player" %>
  <% end %>
  <%= turbo_stream.update "song_details" do %>
    <%= render "songs/empty_song" %>
  <% end %>
<% end %>
<%= turbo_stream.replace "song_stats" do %>
  <%= render "songs/song_stats", user: task.user %>
<% end %>
<%= render_turbo_stream_flash_messages %>