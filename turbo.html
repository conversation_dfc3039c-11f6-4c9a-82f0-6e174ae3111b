<turbo-stream action="replace" target="generation_task_6_song_0"><template>
  <!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
  <!-- Cover Image -->
  <div class="shrink-0">
      <!-- Playable Song - Cover as Button -->
      <a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd">
          <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/1ffb9b37-d828-4997-9776-34d50b1bf8cd/cover.jpeg" alt="Simple Pleasures cover" />

        <!-- Play Icon Overlay - Enhanced for mobile -->
        <div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

        </div>
</a>      </div>
  <!-- Song Info -->
  <div class="flex-1 min-w-0">
    <div class="flex items-center gap-2">
      <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
        <turbo-frame class="contents" id="list_title_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
          Simple Pleasures
</turbo-frame>          </p>
    </div>
    <div class="flex items-center gap-2">
      <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
        3:09
      </p>
      <!-- In Progress Indicator -->
    </div>
  </div>
  <!-- Actions -->
  <div class="inline-flex items-center gap-1">
    <!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
    <!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<form class="button_to" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
  Like
</span>
</button></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
    <!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
    <a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
    <div class="relative">
      <button type="button" 
              class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all"
              data-dropdown-toggle="actions_dropdown_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd"
              data-dropdown-placement="left-start"
              aria-expanded="false">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

        <span class="sr-only">More actions</span>
      </button>
      <!-- Dropdown Menu -->
      <div id="actions_dropdown_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd" 
           class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
        <ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
          <!-- View Details - Only show in dropdown when hidden in main actions -->
          <li class="@sm:hidden">
            <a href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd" 
               data-turbo-frame="song_details"
               class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

              View Details
            </a>
          </li>
            <li>
              <a href="https://apiboxfiles.erweima.ai/MWZmYjliMzctZDgyOC00OTk3LTk3NzYtMzRkNTBiMWJmOGNk.mp3" 
                 target="_blank" 
                 download="Simple_Pleasures.mp3"
                 class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

                Download
              </a>
            </li>
        </ul>
        <div class="py-2">
          <form data-turbo-confirm="Are you sure you want to delete &#39;Simple Pleasures&#39;? This action cannot be undone." class="w-full" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd"><input type="hidden" name="_method" value="delete" autocomplete="off" /><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

            Delete
</button></form>            </div>
      </div>
    </div>
  </div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
</template></turbo-stream>    <turbo-stream method="morph" action="replace" target="song_info_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd"><template>
  <!-- BEGIN app/views/songs/_song_info.html.erb --><!-- Compact Layout Container -->
<div class="flex-1 flex flex-col">
<!-- Top Section: Cover + Title + Actions -->
<div class="p-4 border-b border-purple-200/30 dark:border-gray-700">
<!-- Desktop Layout -->
<div class="hidden md:flex items-start space-x-4">
  <!-- Cover Image (Left) -->
  <div class="flex-shrink-0">
    <!-- BEGIN app/views/songs/_song_cover.html.erb --><!-- Cover Image -->
<div >
<img class="w-20 h-20 rounded-lg shadow-md" src="https://cdn.musicify.me/songs/2/1ffb9b37-d828-4997-9776-34d50b1bf8cd/cover.jpeg" alt="Simple Pleasures cover" />
</div>
<!-- END app/views/songs/_song_cover.html.erb -->
  </div>
  <!-- Title + Status + Actions (Right) -->
  <div class="flex-1 min-w-0">
    <!-- Title Row -->
    <div class="flex items-center justify-between mb-2">
      <!-- BEGIN app/views/songs/_song_title_section.html.erb -->  <!-- Display Mode -->
<div class="flex items-center justify-between w-full">
<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate flex-1 mr-2">
  <turbo-frame class="contents" id="info_title_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
    Simple Pleasures
</turbo-frame>    </h2>
<div class="flex items-center space-x-1 flex-shrink-0">
  <a data-turbo-frame="song_details" class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-gray-700 rounded-full focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all" title="Edit song title" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd/edit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"></path>
</svg>

</a>      <!-- BEGIN app/views/songs/_favorite_button.html.erb --><turbo-frame id="favorite_button_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<form class="button_to" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 rounded-full focus:ring-2 focus:outline-none transition-all hover:scale-105 text-gray-500 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-gray-300 focus:ring-gray-300" title="Add to favorites" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

</button></form></turbo-frame><!-- END app/views/songs/_favorite_button.html.erb -->
</div>
</div>
<!-- END app/views/songs/_song_title_section.html.erb -->
    </div>
    <!-- Status Row -->
    <!-- BEGIN app/views/songs/_song_status.html.erb --><!-- Compact Status Indicator -->
<!-- END app/views/songs/_song_status.html.erb -->
    <!-- Action Buttons Row -->
    <div class="mt-3">
      <!-- BEGIN app/views/songs/_song_actions.html.erb --><!-- Compact Action Buttons -->
<div class="flex items-center space-x-2">
<!-- Play Button -->
<a data-turbo-frame="song_player" class="inline-flex items-center justify-center w-8 h-8 text-white bg-purple-600 rounded-full hover:bg-purple-700 focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all hover:scale-105" title="Play song" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</a>  <!-- Download Button -->
<a href="https://apiboxfiles.erweima.ai/MWZmYjliMzctZDgyOC00OTk3LTk3NzYtMzRkNTBiMWJmOGNk.mp3" target="_blank" download="Simple_Pleasures.mp3"
       class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-white hover:bg-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-600 rounded-full focus:ring-2 focus:outline-none focus:ring-green-300 transition-all hover:scale-105"
       title="Download song">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

</a>
<!-- Delete Button -->
<form data-turbo-confirm="Are you sure you want to delete &#39;Simple Pleasures&#39;? This action cannot be undone." class="button_to" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd"><input type="hidden" name="_method" value="delete" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 text-red-600 hover:text-white hover:bg-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-600 rounded-full focus:ring-2 focus:outline-none focus:ring-red-300 transition-all hover:scale-105" title="Delete song" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

</button></form></div>
<!-- END app/views/songs/_song_actions.html.erb -->
    </div>
  </div>
</div>
<!-- Mobile Layout -->
<div class="md:hidden space-y-4">
  <!-- Cover Image (Centered) -->
  <div class="flex justify-center">
    <!-- BEGIN app/views/songs/_song_cover.html.erb --><!-- Cover Image -->
<div >
<img class="w-20 h-20 rounded-lg shadow-md" src="https://cdn.musicify.me/songs/2/1ffb9b37-d828-4997-9776-34d50b1bf8cd/cover.jpeg" alt="Simple Pleasures cover" />
</div>
<!-- END app/views/songs/_song_cover.html.erb -->
  </div>
  <!-- Title + Actions -->
  <div class="space-y-3">
    <!-- Title Row -->
    <div class="flex items-center justify-between">
      <!-- BEGIN app/views/songs/_song_title_section.html.erb -->  <!-- Display Mode -->
<div class="flex items-center justify-between w-full">
<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate flex-1 mr-2">
  <turbo-frame class="contents" id="info_title_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
    Simple Pleasures
</turbo-frame>    </h2>
<div class="flex items-center space-x-1 flex-shrink-0">
  <a data-turbo-frame="song_details" class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-gray-700 rounded-full focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all" title="Edit song title" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd/edit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"></path>
</svg>

</a>      <!-- BEGIN app/views/songs/_favorite_button.html.erb --><turbo-frame id="favorite_button_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<form class="button_to" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 rounded-full focus:ring-2 focus:outline-none transition-all hover:scale-105 text-gray-500 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-gray-300 focus:ring-gray-300" title="Add to favorites" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

</button></form></turbo-frame><!-- END app/views/songs/_favorite_button.html.erb -->
</div>
</div>
<!-- END app/views/songs/_song_title_section.html.erb -->
    </div>
    <!-- Status Row -->
    <div class="flex justify-center">
      <!-- BEGIN app/views/songs/_song_status.html.erb --><!-- Compact Status Indicator -->
<!-- END app/views/songs/_song_status.html.erb -->
    </div>
    <!-- Action Buttons Row -->
    <div class="flex justify-center">
      <!-- BEGIN app/views/songs/_song_actions.html.erb --><!-- Compact Action Buttons -->
<div class="flex items-center space-x-2">
<!-- Play Button -->
<a data-turbo-frame="song_player" class="inline-flex items-center justify-center w-8 h-8 text-white bg-purple-600 rounded-full hover:bg-purple-700 focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all hover:scale-105" title="Play song" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</a>  <!-- Download Button -->
<a href="https://apiboxfiles.erweima.ai/MWZmYjliMzctZDgyOC00OTk3LTk3NzYtMzRkNTBiMWJmOGNk.mp3" target="_blank" download="Simple_Pleasures.mp3"
       class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-white hover:bg-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-600 rounded-full focus:ring-2 focus:outline-none focus:ring-green-300 transition-all hover:scale-105"
       title="Download song">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

</a>
<!-- Delete Button -->
<form data-turbo-confirm="Are you sure you want to delete &#39;Simple Pleasures&#39;? This action cannot be undone." class="button_to" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd"><input type="hidden" name="_method" value="delete" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 text-red-600 hover:text-white hover:bg-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-600 rounded-full focus:ring-2 focus:outline-none focus:ring-red-300 transition-all hover:scale-105" title="Delete song" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

</button></form></div>
<!-- END app/views/songs/_song_actions.html.erb -->
    </div>
  </div>
</div>
</div>
<!-- Details Section -->
<div class="px-4 py-3 border-b border-purple-200/30 dark:border-gray-700">
<!-- BEGIN app/views/songs/_song_details.html.erb --><!-- Compact Details -->
<div class="text-xs text-gray-500 dark:text-gray-400">
<div class="flex items-center space-x-1 flex-wrap">
<span>Jul 02, 2025</span>
  <span class="text-gray-300 dark:text-gray-600">•</span>
  <span>3:09</span>
</div>
</div>
<!-- END app/views/songs/_song_details.html.erb -->
</div>
<!-- Lyrics Section -->
<div class="flex-1">
<!-- BEGIN app/views/songs/_song_lyrics.html.erb --><!-- Lyrics Section -->
<div class="p-4 flex-1 flex flex-col">
<div class="flex items-center justify-between mb-3">
<h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">Lyrics</h3>
  <button type="button"
          data-collapse-toggle="lyrics-content"
          aria-expanded="true"
          aria-controls="lyrics-content"
          class="inline-flex items-center p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-all">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 transform transition-transform duration-200" id="lyrics-chevron">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 10 4 4 4-4"></path>
</svg>

  </button>
</div>
<div id="lyrics-content" class="flex-1">
  <div class="bg-gradient-to-br from-purple-50/50 via-pink-50/30 to-red-50/50 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50 rounded-lg p-4 max-h-64 overflow-y-auto border border-purple-200/30 dark:border-gray-600">
    <div class="text-gray-700 dark:text-gray-300 text-sm whitespace-pre-line leading-relaxed">
      [Verse]
The sun comes up on a gravel road,
The rooster crows, the day unfolds.
Grandpa&#39;s whittlin&#39; on the porch out back,
With a coffee cup and a burlap sack.

[Verse 2]
Main Street&#39;s quiet, but the diner hums,
Smell of bacon and cinnamon buns.
Folks say &quot;Howdy&quot; as they pass on by,
Small-town life, it don’t need to try.

[Chorus]
It&#39;s the little things that make it right,
The fireflies dancing in the moonlit night.
A front porch swing and a jar of tea,
This simple life means the world to me.

[Verse 3]
The church bell rings, calling hearts to prayer,
The preacher’s voice fills the summer air.
Sunday potlucks with a pie contest,
In a world like this, we’re truly blessed.

[Bridge]
No neon lights, no traffic&#39;s roar,
Just a quiet life and an open door.
Where love runs deep and the roots grow strong,
This is the place where I belong.

[Chorus]
It&#39;s the little things that make it right,
The fireflies dancing in the moonlit night.
A front porch swing and a jar of tea,
This simple life means the world to me.
    </div>
  </div>
</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
const toggleButton = document.querySelector('[data-collapse-toggle="lyrics-content"]');
const content = document.getElementById('lyrics-content');
const chevron = document.getElementById('lyrics-chevron');

if (toggleButton && content && chevron) {
  toggleButton.addEventListener('click', function() {
    const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';

    if (isExpanded) {
      content.style.display = 'none';
      toggleButton.setAttribute('aria-expanded', 'false');
      chevron.style.transform = 'rotate(-90deg)';
    } else {
      content.style.display = 'block';
      toggleButton.setAttribute('aria-expanded', 'true');
      chevron.style.transform = 'rotate(0deg)';
    }
  });
}
});
</script>
<!-- END app/views/songs/_song_lyrics.html.erb -->
</div>
</div>
<!-- END app/views/songs/_song_info.html.erb -->
</template></turbo-stream>    <turbo-stream method="morph" action="replace" target="player_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd_stream"><template>
  <!-- BEGIN app/views/songs/_player.html.erb --><!-- Modern Floating Audio Player -->
<turbo-frame class="contains-player" id="player_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<div class="w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-purple-200/30 dark:border-gray-700 overflow-hidden">
<media-player id="player" class="group/media w-full" autoplay
              stream-type=on-demand
              view-type="audio"
>
  <media-provider class="!contents">
      <source src="https://apiboxfiles.erweima.ai/MWZmYjliMzctZDgyOC00OTk3LTk3NzYtMzRkNTBiMWJmOGNk.mp3" type="audio/mpeg" />
  </media-provider>
  <!-- Controls Container -->
  <media-controls class="w-full">
    <!-- Top Progress Bar -->
    <media-controls-group class="flex w-full items-center px-3">
      <!-- Time Slider -->
      <media-time-slider
    class="group relative mx-[7.5px] inline-flex h-8 w-full cursor-pointer touch-none select-none items-center outline-none">
        <media-slider-chapters class="relative flex h-full w-full items-center rounded-[1px]">
          <template>
            <!-- Slider Chapter -->
            <div class="last-child:mr-0 relative mr-0.5 flex h-full w-full items-center rounded-[1px]"
          style="contain: layout style">
              <!-- Slider Chapter Track -->
              <div
            class="ring-purple-300 dark:ring-purple-600 relative z-0 h-[4px] w-full rounded-sm bg-gray-200 dark:bg-gray-600 group-data-[focus]:ring-[3px]">
                <!-- Current Progress -->
                <div class="bg-purple-600 z-10 absolute h-full w-(--chapter-fill) rounded-sm will-change-[width]">
                </div>
                <!-- Buffer Progress -->
                <div
              class="absolute h-full w-(--chapter-progress) rounded-sm bg-gray-300 dark:bg-gray-500 will-change-[width]">
                </div>
              </div>
            </div>
          </template>
        </media-slider-chapters>
        <!-- Slider Thumb -->
        <div
      class="absolute left-(--slider-fill) top-1/2 z-20 h-[12px] w-[12px] -translate-x-1/2 -translate-y-1/2 rounded-full border border-purple-400 dark:border-purple-300 bg-white dark:bg-gray-200 opacity-0 ring-purple-200/60 dark:ring-purple-600/60 transition-opacity will-change-[left] group-data-[active]:opacity-100 group-data-[dragging]:ring-4 shadow-lg">
        </div>
      </media-time-slider>
    </media-controls-group>
    <!-- Main Player Container with Grid Layout - More Compact -->
    <media-controls-group class="grid grid-cols-3 gap-3 items-center px-4 pb-3">
      <!-- 1. Left Section: Cover Art + Song Info -->
      <div class="flex items-center space-x-3 min-w-0">
        <div class="flex-shrink-0">
          <div id="player__cover-art" class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg overflow-hidden shadow-sm">
              <img src="https://cdn.musicify.me/songs/2/1ffb9b37-d828-4997-9776-34d50b1bf8cd/cover.jpeg" alt="Simple Pleasures cover" class="w-full h-full object-cover">
          </div>
        </div>
        <div class="min-w-0 max-w-xs">
          <p class="text-sm font-semibold text-gray-900 dark:text-white truncate block">
            <turbo-frame class="contents" id="player_title_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
              Simple Pleasures
</turbo-frame>              </p>
          <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 font-mono media-live:hidden">
            <media-time type="current" class="tabular-nums"></media-time>
            <span class="mx-1">/</span>
            <media-time type="duration" class="tabular-nums"></media-time>
          </div>
          <div class="hidden items-center text-xs text-gray-500 dark:text-gray-400 font-mono media-live:flex">
            LIVE
          </div>
        </div>
      </div>
      <!-- 2. Center: Play Button -->
      <div class="flex justify-center">
        <media-play-button class="not-media-can-play:cursor-not-allowed not-media-can-play:bg-gray-200 dark:not-media-can-play:bg-gray-600 group relative w-12 h-12 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewbox="0 0 24 24" class="media-paused:block hidden size-6 text-white ml-0.5">
<path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"></path>
</svg>

          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewbox="0 0 24 24" class="media-paused:hidden size-6 text-white">
<path fill-rule="evenodd" d="M8 5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2H8Zm7 0a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-1Z" clip-rule="evenodd"></path>
</svg>

        </media-play-button>
      </div>
      <!-- 3. Right Section: Action Buttons - More Compact -->
      <div class="flex items-center space-x-1 justify-end">
        <!-- Volume Control Button with Popover -->
        <button 
        data-popover-target="volume-popover" 
        data-popover-placement="top"
        data-popover-offset="4"
        type="button"
        class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" 
        title="Volume">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="hidden size-4 text-gray-600 dark:text-gray-300 media-muted:block">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.5 8.43A4.985 4.985 0 0 1 17 12c0 1.126-.5 2.5-1.5 3.5m2.864-9.864A8.972 8.972 0 0 1 21 12c0 2.023-.5 4.5-2.5 6M7.8 7.5l2.56-2.133a1 1 0 0 1 1.64.768V12m0 4.5v1.365a1 1 0 0 1-1.64.768L6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1m1-4 14 14"></path>
</svg>

          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="block size-4 text-gray-600 dark:text-gray-300 media-muted:hidden">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.5 8.43A4.985 4.985 0 0 1 17 12a4.984 4.984 0 0 1-1.43 3.5m2.794 2.864A8.972 8.972 0 0 0 21 12a8.972 8.972 0 0 0-2.636-6.364M12 6.135v11.73a1 1 0 0 1-1.64.768L6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h2l4.36-3.633a1 1 0 0 1 1.64.768Z"></path>
</svg>

        </button>
        <!-- Volume Popover -->
        <div data-popover id="volume-popover" role="tooltip" class="px-2 absolute z-10 invisible inline-block text-sm text-gray-500 dark:text-gray-400 transition-opacity duration-300 bg-white dark:bg-gray-700 border border-purple-200/30 dark:border-gray-600 rounded-lg shadow-xl opacity-0">
          <media-volume-slider
              class="group relative mx-2 inline-flex h-8 w-[100px] cursor-pointer touch-none select-none items-center outline-none">
            <!-- Slider Track -->
            <div class="ring-purple-300 dark:ring-purple-600 relative z-0 h-[4px] w-full rounded-sm bg-gray-200 dark:bg-gray-600 group-data-[focus]:ring-[3px]">
              <!-- Current Volume -->
              <div class="bg-purple-600 z-10 absolute h-full w-(--slider-fill) rounded-sm will-change-[width]"></div>
            </div>
            <!-- Slider Thumb -->
            <div
                class="absolute left-(--slider-fill) top-1/2 z-20 h-[12px] w-[12px] -translate-x-1/2 -translate-y-1/2 rounded-full border border-purple-400 dark:border-purple-300 bg-white dark:bg-gray-200 ring-purple-200/60 dark:ring-purple-600/60 will-change-[left] group-data-[dragging]:ring-4 shadow-lg">
            </div>
          </media-volume-slider>
        </div>
        <!-- Like Button -->
        <!-- BEGIN app/views/songs/_player_favorite_button.html.erb -->  <turbo-frame id="player_favorite_button_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<form class="button_to" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" type="submit">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 text-gray-600 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

  <span class="sr-only">
    Like
  </span>
</button></form></turbo-frame><!-- END app/views/songs/_player_favorite_button.html.erb -->
        <!-- Download Button -->
            <a href="https://apiboxfiles.erweima.ai/MWZmYjliMzctZDgyOC00OTk3LTk3NzYtMzRkNTBiMWJmOGNk.mp3" target="_blank" download="Simple_Pleasures.mp3"
                   class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" 
                   title="Download">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 text-gray-600 dark:text-gray-300">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

            </a>
      </div>
    </media-controls-group>
  </media-controls>
</media-player>
</div>
</turbo-frame><!-- END app/views/songs/_player.html.erb -->
</template></turbo-stream>    <turbo-stream action="replace" target="generation_task_6_song_1"><template>
  <!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_8b621854-d538-4c58-92c5-d405451f715a">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
  <!-- Cover Image -->
  <div class="shrink-0">
      <!-- Playable Song - Cover as Button -->
      <a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/8b621854-d538-4c58-92c5-d405451f715a">
          <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/8b621854-d538-4c58-92c5-d405451f715a/cover.jpeg" alt="Simple Pleasures cover" />

        <!-- Play Icon Overlay - Enhanced for mobile -->
        <div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

        </div>
</a>      </div>
  <!-- Song Info -->
  <div class="flex-1 min-w-0">
    <div class="flex items-center gap-2">
      <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
        <turbo-frame class="contents" id="list_title_song_8b621854-d538-4c58-92c5-d405451f715a">
          Simple Pleasures
</turbo-frame>          </p>
    </div>
    <div class="flex items-center gap-2">
      <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
        2:55
      </p>
      <!-- In Progress Indicator -->
    </div>
  </div>
  <!-- Actions -->
  <div class="inline-flex items-center gap-1">
    <!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
    <!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_8b621854-d538-4c58-92c5-d405451f715a">
<form class="button_to" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
  Like
</span>
</button></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
    <!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
    <a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/8b621854-d538-4c58-92c5-d405451f715a">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
    <div class="relative">
      <button type="button" 
              class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all"
              data-dropdown-toggle="actions_dropdown_song_8b621854-d538-4c58-92c5-d405451f715a"
              data-dropdown-placement="left-start"
              aria-expanded="false">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

        <span class="sr-only">More actions</span>
      </button>
      <!-- Dropdown Menu -->
      <div id="actions_dropdown_song_8b621854-d538-4c58-92c5-d405451f715a" 
           class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
        <ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
          <!-- View Details - Only show in dropdown when hidden in main actions -->
          <li class="@sm:hidden">
            <a href="/songs/8b621854-d538-4c58-92c5-d405451f715a" 
               data-turbo-frame="song_details"
               class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

              View Details
            </a>
          </li>
            <li>
              <a href="https://apiboxfiles.erweima.ai/OGI2MjE4NTQtZDUzOC00YzU4LTkyYzUtZDQwNTQ1MWY3MTVh.mp3" 
                 target="_blank" 
                 download="Simple_Pleasures.mp3"
                 class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

                Download
              </a>
            </li>
        </ul>
        <div class="py-2">
          <form data-turbo-confirm="Are you sure you want to delete &#39;Simple Pleasures&#39;? This action cannot be undone." class="w-full" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a"><input type="hidden" name="_method" value="delete" autocomplete="off" /><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

            Delete
</button></form>            </div>
      </div>
    </div>
  </div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
</template></turbo-stream>    <turbo-stream method="morph" action="replace" target="song_info_song_8b621854-d538-4c58-92c5-d405451f715a"><template>
  <!-- BEGIN app/views/songs/_song_info.html.erb --><!-- Compact Layout Container -->
<div class="flex-1 flex flex-col">
<!-- Top Section: Cover + Title + Actions -->
<div class="p-4 border-b border-purple-200/30 dark:border-gray-700">
<!-- Desktop Layout -->
<div class="hidden md:flex items-start space-x-4">
  <!-- Cover Image (Left) -->
  <div class="flex-shrink-0">
    <!-- BEGIN app/views/songs/_song_cover.html.erb --><!-- Cover Image -->
<div >
<img class="w-20 h-20 rounded-lg shadow-md" src="https://cdn.musicify.me/songs/2/8b621854-d538-4c58-92c5-d405451f715a/cover.jpeg" alt="Simple Pleasures cover" />
</div>
<!-- END app/views/songs/_song_cover.html.erb -->
  </div>
  <!-- Title + Status + Actions (Right) -->
  <div class="flex-1 min-w-0">
    <!-- Title Row -->
    <div class="flex items-center justify-between mb-2">
      <!-- BEGIN app/views/songs/_song_title_section.html.erb -->  <!-- Display Mode -->
<div class="flex items-center justify-between w-full">
<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate flex-1 mr-2">
  <turbo-frame class="contents" id="info_title_song_8b621854-d538-4c58-92c5-d405451f715a">
    Simple Pleasures
</turbo-frame>    </h2>
<div class="flex items-center space-x-1 flex-shrink-0">
  <a data-turbo-frame="song_details" class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-gray-700 rounded-full focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all" title="Edit song title" href="/songs/8b621854-d538-4c58-92c5-d405451f715a/edit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"></path>
</svg>

</a>      <!-- BEGIN app/views/songs/_favorite_button.html.erb --><turbo-frame id="favorite_button_song_8b621854-d538-4c58-92c5-d405451f715a">
<form class="button_to" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 rounded-full focus:ring-2 focus:outline-none transition-all hover:scale-105 text-gray-500 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-gray-300 focus:ring-gray-300" title="Add to favorites" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

</button></form></turbo-frame><!-- END app/views/songs/_favorite_button.html.erb -->
</div>
</div>
<!-- END app/views/songs/_song_title_section.html.erb -->
    </div>
    <!-- Status Row -->
    <!-- BEGIN app/views/songs/_song_status.html.erb --><!-- Compact Status Indicator -->
<!-- END app/views/songs/_song_status.html.erb -->
    <!-- Action Buttons Row -->
    <div class="mt-3">
      <!-- BEGIN app/views/songs/_song_actions.html.erb --><!-- Compact Action Buttons -->
<div class="flex items-center space-x-2">
<!-- Play Button -->
<a data-turbo-frame="song_player" class="inline-flex items-center justify-center w-8 h-8 text-white bg-purple-600 rounded-full hover:bg-purple-700 focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all hover:scale-105" title="Play song" href="/songs/8b621854-d538-4c58-92c5-d405451f715a">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</a>  <!-- Download Button -->
<a href="https://apiboxfiles.erweima.ai/OGI2MjE4NTQtZDUzOC00YzU4LTkyYzUtZDQwNTQ1MWY3MTVh.mp3" target="_blank" download="Simple_Pleasures.mp3"
       class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-white hover:bg-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-600 rounded-full focus:ring-2 focus:outline-none focus:ring-green-300 transition-all hover:scale-105"
       title="Download song">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

</a>
<!-- Delete Button -->
<form data-turbo-confirm="Are you sure you want to delete &#39;Simple Pleasures&#39;? This action cannot be undone." class="button_to" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a"><input type="hidden" name="_method" value="delete" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 text-red-600 hover:text-white hover:bg-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-600 rounded-full focus:ring-2 focus:outline-none focus:ring-red-300 transition-all hover:scale-105" title="Delete song" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

</button></form></div>
<!-- END app/views/songs/_song_actions.html.erb -->
    </div>
  </div>
</div>
<!-- Mobile Layout -->
<div class="md:hidden space-y-4">
  <!-- Cover Image (Centered) -->
  <div class="flex justify-center">
    <!-- BEGIN app/views/songs/_song_cover.html.erb --><!-- Cover Image -->
<div >
<img class="w-20 h-20 rounded-lg shadow-md" src="https://cdn.musicify.me/songs/2/8b621854-d538-4c58-92c5-d405451f715a/cover.jpeg" alt="Simple Pleasures cover" />
</div>
<!-- END app/views/songs/_song_cover.html.erb -->
  </div>
  <!-- Title + Actions -->
  <div class="space-y-3">
    <!-- Title Row -->
    <div class="flex items-center justify-between">
      <!-- BEGIN app/views/songs/_song_title_section.html.erb -->  <!-- Display Mode -->
<div class="flex items-center justify-between w-full">
<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate flex-1 mr-2">
  <turbo-frame class="contents" id="info_title_song_8b621854-d538-4c58-92c5-d405451f715a">
    Simple Pleasures
</turbo-frame>    </h2>
<div class="flex items-center space-x-1 flex-shrink-0">
  <a data-turbo-frame="song_details" class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-gray-700 rounded-full focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all" title="Edit song title" href="/songs/8b621854-d538-4c58-92c5-d405451f715a/edit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"></path>
</svg>

</a>      <!-- BEGIN app/views/songs/_favorite_button.html.erb --><turbo-frame id="favorite_button_song_8b621854-d538-4c58-92c5-d405451f715a">
<form class="button_to" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 rounded-full focus:ring-2 focus:outline-none transition-all hover:scale-105 text-gray-500 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-gray-300 focus:ring-gray-300" title="Add to favorites" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

</button></form></turbo-frame><!-- END app/views/songs/_favorite_button.html.erb -->
</div>
</div>
<!-- END app/views/songs/_song_title_section.html.erb -->
    </div>
    <!-- Status Row -->
    <div class="flex justify-center">
      <!-- BEGIN app/views/songs/_song_status.html.erb --><!-- Compact Status Indicator -->
<!-- END app/views/songs/_song_status.html.erb -->
    </div>
    <!-- Action Buttons Row -->
    <div class="flex justify-center">
      <!-- BEGIN app/views/songs/_song_actions.html.erb --><!-- Compact Action Buttons -->
<div class="flex items-center space-x-2">
<!-- Play Button -->
<a data-turbo-frame="song_player" class="inline-flex items-center justify-center w-8 h-8 text-white bg-purple-600 rounded-full hover:bg-purple-700 focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all hover:scale-105" title="Play song" href="/songs/8b621854-d538-4c58-92c5-d405451f715a">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</a>  <!-- Download Button -->
<a href="https://apiboxfiles.erweima.ai/OGI2MjE4NTQtZDUzOC00YzU4LTkyYzUtZDQwNTQ1MWY3MTVh.mp3" target="_blank" download="Simple_Pleasures.mp3"
       class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-white hover:bg-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-600 rounded-full focus:ring-2 focus:outline-none focus:ring-green-300 transition-all hover:scale-105"
       title="Download song">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

</a>
<!-- Delete Button -->
<form data-turbo-confirm="Are you sure you want to delete &#39;Simple Pleasures&#39;? This action cannot be undone." class="button_to" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a"><input type="hidden" name="_method" value="delete" autocomplete="off" /><button class="inline-flex items-center justify-center w-8 h-8 text-red-600 hover:text-white hover:bg-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-600 rounded-full focus:ring-2 focus:outline-none focus:ring-red-300 transition-all hover:scale-105" title="Delete song" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

</button></form></div>
<!-- END app/views/songs/_song_actions.html.erb -->
    </div>
  </div>
</div>
</div>
<!-- Details Section -->
<div class="px-4 py-3 border-b border-purple-200/30 dark:border-gray-700">
<!-- BEGIN app/views/songs/_song_details.html.erb --><!-- Compact Details -->
<div class="text-xs text-gray-500 dark:text-gray-400">
<div class="flex items-center space-x-1 flex-wrap">
<span>Jul 02, 2025</span>
  <span class="text-gray-300 dark:text-gray-600">•</span>
  <span>2:55</span>
</div>
</div>
<!-- END app/views/songs/_song_details.html.erb -->
</div>
<!-- Lyrics Section -->
<div class="flex-1">
<!-- BEGIN app/views/songs/_song_lyrics.html.erb --><!-- Lyrics Section -->
<div class="p-4 flex-1 flex flex-col">
<div class="flex items-center justify-between mb-3">
<h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">Lyrics</h3>
  <button type="button"
          data-collapse-toggle="lyrics-content"
          aria-expanded="true"
          aria-controls="lyrics-content"
          class="inline-flex items-center p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-all">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 transform transition-transform duration-200" id="lyrics-chevron">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 10 4 4 4-4"></path>
</svg>

  </button>
</div>
<div id="lyrics-content" class="flex-1">
  <div class="bg-gradient-to-br from-purple-50/50 via-pink-50/30 to-red-50/50 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50 rounded-lg p-4 max-h-64 overflow-y-auto border border-purple-200/30 dark:border-gray-600">
    <div class="text-gray-700 dark:text-gray-300 text-sm whitespace-pre-line leading-relaxed">
      [Verse]
The sun comes up on a gravel road,
The rooster crows, the day unfolds.
Grandpa&#39;s whittlin&#39; on the porch out back,
With a coffee cup and a burlap sack.

[Verse 2]
Main Street&#39;s quiet, but the diner hums,
Smell of bacon and cinnamon buns.
Folks say &quot;Howdy&quot; as they pass on by,
Small-town life, it don’t need to try.

[Chorus]
It&#39;s the little things that make it right,
The fireflies dancing in the moonlit night.
A front porch swing and a jar of tea,
This simple life means the world to me.

[Verse 3]
The church bell rings, calling hearts to prayer,
The preacher’s voice fills the summer air.
Sunday potlucks with a pie contest,
In a world like this, we’re truly blessed.

[Bridge]
No neon lights, no traffic&#39;s roar,
Just a quiet life and an open door.
Where love runs deep and the roots grow strong,
This is the place where I belong.

[Chorus]
It&#39;s the little things that make it right,
The fireflies dancing in the moonlit night.
A front porch swing and a jar of tea,
This simple life means the world to me.
    </div>
  </div>
</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
const toggleButton = document.querySelector('[data-collapse-toggle="lyrics-content"]');
const content = document.getElementById('lyrics-content');
const chevron = document.getElementById('lyrics-chevron');

if (toggleButton && content && chevron) {
  toggleButton.addEventListener('click', function() {
    const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';

    if (isExpanded) {
      content.style.display = 'none';
      toggleButton.setAttribute('aria-expanded', 'false');
      chevron.style.transform = 'rotate(-90deg)';
    } else {
      content.style.display = 'block';
      toggleButton.setAttribute('aria-expanded', 'true');
      chevron.style.transform = 'rotate(0deg)';
    }
  });
}
});
</script>
<!-- END app/views/songs/_song_lyrics.html.erb -->
</div>
</div>
<!-- END app/views/songs/_song_info.html.erb -->
</template></turbo-stream>    <turbo-stream method="morph" action="replace" target="player_song_8b621854-d538-4c58-92c5-d405451f715a_stream"><template>
  <!-- BEGIN app/views/songs/_player.html.erb --><!-- Modern Floating Audio Player -->
<turbo-frame class="contains-player" id="player_song_8b621854-d538-4c58-92c5-d405451f715a">
<div class="w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-purple-200/30 dark:border-gray-700 overflow-hidden">
<media-player id="player" class="group/media w-full" autoplay
              stream-type=on-demand
              view-type="audio"
>
  <media-provider class="!contents">
      <source src="https://apiboxfiles.erweima.ai/OGI2MjE4NTQtZDUzOC00YzU4LTkyYzUtZDQwNTQ1MWY3MTVh.mp3" type="audio/mpeg" />
  </media-provider>
  <!-- Controls Container -->
  <media-controls class="w-full">
    <!-- Top Progress Bar -->
    <media-controls-group class="flex w-full items-center px-3">
      <!-- Time Slider -->
      <media-time-slider
    class="group relative mx-[7.5px] inline-flex h-8 w-full cursor-pointer touch-none select-none items-center outline-none">
        <media-slider-chapters class="relative flex h-full w-full items-center rounded-[1px]">
          <template>
            <!-- Slider Chapter -->
            <div class="last-child:mr-0 relative mr-0.5 flex h-full w-full items-center rounded-[1px]"
          style="contain: layout style">
              <!-- Slider Chapter Track -->
              <div
            class="ring-purple-300 dark:ring-purple-600 relative z-0 h-[4px] w-full rounded-sm bg-gray-200 dark:bg-gray-600 group-data-[focus]:ring-[3px]">
                <!-- Current Progress -->
                <div class="bg-purple-600 z-10 absolute h-full w-(--chapter-fill) rounded-sm will-change-[width]">
                </div>
                <!-- Buffer Progress -->
                <div
              class="absolute h-full w-(--chapter-progress) rounded-sm bg-gray-300 dark:bg-gray-500 will-change-[width]">
                </div>
              </div>
            </div>
          </template>
        </media-slider-chapters>
        <!-- Slider Thumb -->
        <div
      class="absolute left-(--slider-fill) top-1/2 z-20 h-[12px] w-[12px] -translate-x-1/2 -translate-y-1/2 rounded-full border border-purple-400 dark:border-purple-300 bg-white dark:bg-gray-200 opacity-0 ring-purple-200/60 dark:ring-purple-600/60 transition-opacity will-change-[left] group-data-[active]:opacity-100 group-data-[dragging]:ring-4 shadow-lg">
        </div>
      </media-time-slider>
    </media-controls-group>
    <!-- Main Player Container with Grid Layout - More Compact -->
    <media-controls-group class="grid grid-cols-3 gap-3 items-center px-4 pb-3">
      <!-- 1. Left Section: Cover Art + Song Info -->
      <div class="flex items-center space-x-3 min-w-0">
        <div class="flex-shrink-0">
          <div id="player__cover-art" class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg overflow-hidden shadow-sm">
              <img src="https://cdn.musicify.me/songs/2/8b621854-d538-4c58-92c5-d405451f715a/cover.jpeg" alt="Simple Pleasures cover" class="w-full h-full object-cover">
          </div>
        </div>
        <div class="min-w-0 max-w-xs">
          <p class="text-sm font-semibold text-gray-900 dark:text-white truncate block">
            <turbo-frame class="contents" id="player_title_song_8b621854-d538-4c58-92c5-d405451f715a">
              Simple Pleasures
</turbo-frame>              </p>
          <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 font-mono media-live:hidden">
            <media-time type="current" class="tabular-nums"></media-time>
            <span class="mx-1">/</span>
            <media-time type="duration" class="tabular-nums"></media-time>
          </div>
          <div class="hidden items-center text-xs text-gray-500 dark:text-gray-400 font-mono media-live:flex">
            LIVE
          </div>
        </div>
      </div>
      <!-- 2. Center: Play Button -->
      <div class="flex justify-center">
        <media-play-button class="not-media-can-play:cursor-not-allowed not-media-can-play:bg-gray-200 dark:not-media-can-play:bg-gray-600 group relative w-12 h-12 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewbox="0 0 24 24" class="media-paused:block hidden size-6 text-white ml-0.5">
<path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"></path>
</svg>

          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewbox="0 0 24 24" class="media-paused:hidden size-6 text-white">
<path fill-rule="evenodd" d="M8 5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2H8Zm7 0a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-1Z" clip-rule="evenodd"></path>
</svg>

        </media-play-button>
      </div>
      <!-- 3. Right Section: Action Buttons - More Compact -->
      <div class="flex items-center space-x-1 justify-end">
        <!-- Volume Control Button with Popover -->
        <button 
        data-popover-target="volume-popover" 
        data-popover-placement="top"
        data-popover-offset="4"
        type="button"
        class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" 
        title="Volume">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="hidden size-4 text-gray-600 dark:text-gray-300 media-muted:block">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.5 8.43A4.985 4.985 0 0 1 17 12c0 1.126-.5 2.5-1.5 3.5m2.864-9.864A8.972 8.972 0 0 1 21 12c0 2.023-.5 4.5-2.5 6M7.8 7.5l2.56-2.133a1 1 0 0 1 1.64.768V12m0 4.5v1.365a1 1 0 0 1-1.64.768L6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1m1-4 14 14"></path>
</svg>

          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="block size-4 text-gray-600 dark:text-gray-300 media-muted:hidden">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.5 8.43A4.985 4.985 0 0 1 17 12a4.984 4.984 0 0 1-1.43 3.5m2.794 2.864A8.972 8.972 0 0 0 21 12a8.972 8.972 0 0 0-2.636-6.364M12 6.135v11.73a1 1 0 0 1-1.64.768L6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h2l4.36-3.633a1 1 0 0 1 1.64.768Z"></path>
</svg>

        </button>
        <!-- Volume Popover -->
        <div data-popover id="volume-popover" role="tooltip" class="px-2 absolute z-10 invisible inline-block text-sm text-gray-500 dark:text-gray-400 transition-opacity duration-300 bg-white dark:bg-gray-700 border border-purple-200/30 dark:border-gray-600 rounded-lg shadow-xl opacity-0">
          <media-volume-slider
              class="group relative mx-2 inline-flex h-8 w-[100px] cursor-pointer touch-none select-none items-center outline-none">
            <!-- Slider Track -->
            <div class="ring-purple-300 dark:ring-purple-600 relative z-0 h-[4px] w-full rounded-sm bg-gray-200 dark:bg-gray-600 group-data-[focus]:ring-[3px]">
              <!-- Current Volume -->
              <div class="bg-purple-600 z-10 absolute h-full w-(--slider-fill) rounded-sm will-change-[width]"></div>
            </div>
            <!-- Slider Thumb -->
            <div
                class="absolute left-(--slider-fill) top-1/2 z-20 h-[12px] w-[12px] -translate-x-1/2 -translate-y-1/2 rounded-full border border-purple-400 dark:border-purple-300 bg-white dark:bg-gray-200 ring-purple-200/60 dark:ring-purple-600/60 will-change-[left] group-data-[dragging]:ring-4 shadow-lg">
            </div>
          </media-volume-slider>
        </div>
        <!-- Like Button -->
        <!-- BEGIN app/views/songs/_player_favorite_button.html.erb -->  <turbo-frame id="player_favorite_button_song_8b621854-d538-4c58-92c5-d405451f715a">
<form class="button_to" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off" /><button class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" type="submit">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 text-gray-600 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

  <span class="sr-only">
    Like
  </span>
</button></form></turbo-frame><!-- END app/views/songs/_player_favorite_button.html.erb -->
        <!-- Download Button -->
            <a href="https://apiboxfiles.erweima.ai/OGI2MjE4NTQtZDUzOC00YzU4LTkyYzUtZDQwNTQ1MWY3MTVh.mp3" target="_blank" download="Simple_Pleasures.mp3"
                   class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" 
                   title="Download">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24" class="size-4 text-gray-600 dark:text-gray-300">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

            </a>
      </div>
    </media-controls-group>
  </media-controls>
</media-player>
</div>
</turbo-frame><!-- END app/views/songs/_player.html.erb -->
</template></turbo-stream><turbo-stream action="replace" target="song_stats"><template>
<!-- BEGIN app/views/songs/_song_stats.html.erb --><turbo-frame class="text-sm text-gray-600 mt-1" id="song_stats">
10 songs
</turbo-frame><!-- END app/views/songs/_song_stats.html.erb -->
</template></turbo-stream><turbo-stream action="prepend" target="toast-container"><template><!-- BEGIN app/views/shared/_flash.html.erb --><div id="toast-container" class="fixed top-5 right-5 z-50 space-y-4">
</div><!-- END app/views/shared/_flash.html.erb --></template></turbo-stream>