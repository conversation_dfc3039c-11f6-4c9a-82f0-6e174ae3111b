<div class="flex-1 overflow-y-auto flex flex-col">
  <!-- BEGIN app/views/songs/_song_list.html.erb --><ul role="list" class="divide-y divide-gray-200 peer" id="songs_list">
<!-- BEGIN app/views/generations/_placeholder.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_8b621854-d538-4c58-92c5-d405451f715a">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/8b621854-d538-4c58-92c5-d405451f715a">
  <img class="w-full h-full object-cover" src="https://apiboxfiles.erweima.ai/OGI2MjE4NTQtZDUzOC00YzU4LTkyYzUtZDQwNTQ1MWY3MTVh.jpeg" alt="Simple Pleasures cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_8b621854-d538-4c58-92c5-d405451f715a">
  Simple Pleasures
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
Streaming...
</p>
<!-- In Progress Indicator -->
<div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 whitespace-nowrap">
  <!-- BEGIN app/views/shared/_loading_icon.html.erb --><svg class="size-3 mr-1 animate-spin" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg>
<!-- END app/views/shared/_loading_icon.html.erb -->
  In Progress
</div>
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_8b621854-d538-4c58-92c5-d405451f715a">
<form class="button_to" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/8b621854-d538-4c58-92c5-d405451f715a">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_8b621854-d538-4c58-92c5-d405451f715a" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_8b621854-d538-4c58-92c5-d405451f715a" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 306px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/8b621854-d538-4c58-92c5-d405451f715a" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <div class="flex items-center px-4 py-2 text-gray-400 dark:text-gray-500 cursor-not-allowed">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

          Download
      </div>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Simple Pleasures'? This action cannot be undone." class="w-full" method="post" action="/songs/8b621854-d538-4c58-92c5-d405451f715a"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- END app/views/generations/_placeholder.html.erb -->
<!-- BEGIN app/views/generations/_placeholder.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd">
  <img class="w-full h-full object-cover" src="https://apiboxfiles.erweima.ai/MWZmYjliMzctZDgyOC00OTk3LTk3NzYtMzRkNTBiMWJmOGNk.jpeg" alt="Simple Pleasures cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
  Simple Pleasures
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
Streaming...
</p>
<!-- In Progress Indicator -->
<div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 whitespace-nowrap">
  <!-- BEGIN app/views/shared/_loading_icon.html.erb --><svg class="size-3 mr-1 animate-spin" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg>
<!-- END app/views/shared/_loading_icon.html.erb -->
  In Progress
</div>
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<form class="button_to" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_1ffb9b37-d828-4997-9776-34d50b1bf8cd" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 378px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <div class="flex items-center px-4 py-2 text-gray-400 dark:text-gray-500 cursor-not-allowed">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

          Download
      </div>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Simple Pleasures'? This action cannot be undone." class="w-full" method="post" action="/songs/1ffb9b37-d828-4997-9776-34d50b1bf8cd"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- END app/views/generations/_placeholder.html.erb -->

<!-- Generation Task Placeholders (2 per task) -->
<!-- Failed Generation Tasks (2 per task) -->
<!-- Actual Songs -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_6016166d-326e-46eb-b755-ae940b1cbc94">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/6016166d-326e-46eb-b755-ae940b1cbc94">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/6016166d-326e-46eb-b755-ae940b1cbc94/cover.jpeg" alt="Break These Chains cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_6016166d-326e-46eb-b755-ae940b1cbc94">
  Break These Chains
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
3:33
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_6016166d-326e-46eb-b755-ae940b1cbc94">
<form class="button_to" method="post" action="/songs/6016166d-326e-46eb-b755-ae940b1cbc94/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button><input type="hidden" name="authenticity_token" value="NIMNEelus9uGJR0VX6GqhTLNzrxwRFXz1xJfZzXd_25GXgQmGT9d0wIixaG0m8b3S5g68jAVKuPa3o8j67OwQA" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/6016166d-326e-46eb-b755-ae940b1cbc94">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_6016166d-326e-46eb-b755-ae940b1cbc94" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_6016166d-326e-46eb-b755-ae940b1cbc94" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 450px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/6016166d-326e-46eb-b755-ae940b1cbc94" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/6016166d-326e-46eb-b755-ae940b1cbc94/audio.mp3" target="_blank" download="Break_These_Chains.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Break These Chains'? This action cannot be undone." class="w-full" method="post" action="/songs/6016166d-326e-46eb-b755-ae940b1cbc94"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="NHZU_mJihmkFacukhhK6GZrEHePBRJMToW_tWP8z94pvvH7dhht3j_K3cBKVY1r3IoqiRM0A4Iu5SFJ0YgOsqw" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_88c320ef-85a1-4ff2-a78c-495ec453d5b5">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/88c320ef-85a1-4ff2-a78c-495ec453d5b5">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/88c320ef-85a1-4ff2-a78c-495ec453d5b5/cover.jpeg" alt="Break These Chains cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_88c320ef-85a1-4ff2-a78c-495ec453d5b5">
  Break These Chains
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
3:11
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_88c320ef-85a1-4ff2-a78c-495ec453d5b5">
<form class="button_to" method="post" action="/songs/88c320ef-85a1-4ff2-a78c-495ec453d5b5/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button><input type="hidden" name="authenticity_token" value="gCrtcByAFhvf21VWv1z-3gb0YybVcEkXtG3mypTU1CBzE5_EAIFas4gZK_hPh6c431GwOZdoavjQOSYYsHCrzg" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/88c320ef-85a1-4ff2-a78c-495ec453d5b5">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_88c320ef-85a1-4ff2-a78c-495ec453d5b5" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_88c320ef-85a1-4ff2-a78c-495ec453d5b5" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 522px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/88c320ef-85a1-4ff2-a78c-495ec453d5b5" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/88c320ef-85a1-4ff2-a78c-495ec453d5b5/audio.mp3" target="_blank" download="Break_These_Chains.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Break These Chains'? This action cannot be undone." class="w-full" method="post" action="/songs/88c320ef-85a1-4ff2-a78c-495ec453d5b5"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="tKInHeFQJVV0tRcKocvM7qYjtfPiiqDjg0rqJENKuQI4MDnmUsCC7vKvAG-jn9tgWR2NBPX3VC5_VE99G4L-Pw" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_2f7d5357-62fc-4f8a-a19a-2d5e18834e1d">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/2f7d5357-62fc-4f8a-a19a-2d5e18834e1d">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/2f7d5357-62fc-4f8a-a19a-2d5e18834e1d/cover.jpeg" alt="Chasing the Horizon cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_2f7d5357-62fc-4f8a-a19a-2d5e18834e1d">
  Chasing the Horizon
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
3:13
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_2f7d5357-62fc-4f8a-a19a-2d5e18834e1d">
<form class="button_to" method="post" action="/songs/2f7d5357-62fc-4f8a-a19a-2d5e18834e1d/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button><input type="hidden" name="authenticity_token" value="kiamujN7m5q55jbxhF_b-6SjAMPHGrzPKD7r3dlqbE3lvC_IrZdwWBClf3z0dHaZBBps-44KaDY23ns6nF_IpQ" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/2f7d5357-62fc-4f8a-a19a-2d5e18834e1d">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_2f7d5357-62fc-4f8a-a19a-2d5e18834e1d" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_2f7d5357-62fc-4f8a-a19a-2d5e18834e1d" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 594px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/2f7d5357-62fc-4f8a-a19a-2d5e18834e1d" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/2f7d5357-62fc-4f8a-a19a-2d5e18834e1d/audio.mp3" target="_blank" download="Chasing_the_Horizon.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Chasing the Horizon'? This action cannot be undone." class="w-full" method="post" action="/songs/2f7d5357-62fc-4f8a-a19a-2d5e18834e1d"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="2bYw9XAZKw4G6SsnfLbdZbjztXgLhQsMOs9PkzKk2GNPYF8SA5UlEZtl81w0TtWaKzs6VsXhzl0uRxinYlLfrA" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_f4324aa4-9b9a-4947-9842-356c8087cffa">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/f4324aa4-9b9a-4947-9842-356c8087cffa">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/f4324aa4-9b9a-4947-9842-356c8087cffa/cover.jpeg" alt="Chasing the Horizon cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_f4324aa4-9b9a-4947-9842-356c8087cffa">
  Chasing the Horizon
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
3:07
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_f4324aa4-9b9a-4947-9842-356c8087cffa">
<form class="button_to" method="post" action="/songs/f4324aa4-9b9a-4947-9842-356c8087cffa/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button><input type="hidden" name="authenticity_token" value="iXpXTEssdGNmIfmXE35SM4AuW6ns2euuqKZSEsLG9jxwCIAxFpG5QwK7_dRqRlNW0zDs55cIRb9-WwybdZKKUQ" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/f4324aa4-9b9a-4947-9842-356c8087cffa">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_f4324aa4-9b9a-4947-9842-356c8087cffa" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_f4324aa4-9b9a-4947-9842-356c8087cffa" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 666px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/f4324aa4-9b9a-4947-9842-356c8087cffa" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/f4324aa4-9b9a-4947-9842-356c8087cffa/audio.mp3" target="_blank" download="Chasing_the_Horizon.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Chasing the Horizon'? This action cannot be undone." class="w-full" method="post" action="/songs/f4324aa4-9b9a-4947-9842-356c8087cffa"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="Q4fnnQGhpsU5fdGS-Ll-rxanpgzHxuCoRGnF88067OhO3bH5oJhHMRrksP-DQrvFntp1P5r-nQSzDatGaKCNFg" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_59bef4cd-1b18-4717-be3e-b1aa25612648">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/59bef4cd-1b18-4717-be3e-b1aa25612648">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/59bef4cd-1b18-4717-be3e-b1aa25612648/cover.jpeg" alt="Clash of Titans cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_59bef4cd-1b18-4717-be3e-b1aa25612648">
  Clash of Titans
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
3:31
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_59bef4cd-1b18-4717-be3e-b1aa25612648">
<form class="button_to" method="post" action="/songs/59bef4cd-1b18-4717-be3e-b1aa25612648/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button><input type="hidden" name="authenticity_token" value="sAcTQ3VQjYSEo9xIvQxsG5v7uDx2I41RDi58JozBOT9zMnRi7bCvncaKpNdUwyBEjiyF1ElA912H2l1MZRyrpg" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/59bef4cd-1b18-4717-be3e-b1aa25612648">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_59bef4cd-1b18-4717-be3e-b1aa25612648" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_59bef4cd-1b18-4717-be3e-b1aa25612648" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 738px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/59bef4cd-1b18-4717-be3e-b1aa25612648" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/59bef4cd-1b18-4717-be3e-b1aa25612648/audio.mp3" target="_blank" download="Clash_of_Titans.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Clash of Titans'? This action cannot be undone." class="w-full" method="post" action="/songs/59bef4cd-1b18-4717-be3e-b1aa25612648"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="92wBscT5O72XYurkFdowcN1yqgx0MGavHUnHiaydv4GViBpxJALh4jgHixRGPWoXjc5zpxSQBfx3NPvyxaZQOw" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_aaa7c137-5dcb-428d-9114-fac7501e5538">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/aaa7c137-5dcb-428d-9114-fac7501e5538">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/aaa7c137-5dcb-428d-9114-fac7501e5538/cover.jpeg" alt="Clash of Titans cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_aaa7c137-5dcb-428d-9114-fac7501e5538">
  Clash of Titans
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
2:45
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_aaa7c137-5dcb-428d-9114-fac7501e5538">
<form class="button_to" method="post" action="/songs/aaa7c137-5dcb-428d-9114-fac7501e5538/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button><input type="hidden" name="authenticity_token" value="V04-r_z1hL917f5L7d-gWndiYvZJwE7inxuvtb0qkajXorjMDHaglFrMGgCm9xn2jjHv1vtwfNEErArJW_SM8g" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/aaa7c137-5dcb-428d-9114-fac7501e5538">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_aaa7c137-5dcb-428d-9114-fac7501e5538" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_aaa7c137-5dcb-428d-9114-fac7501e5538" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 810px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/aaa7c137-5dcb-428d-9114-fac7501e5538" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/aaa7c137-5dcb-428d-9114-fac7501e5538/audio.mp3" target="_blank" download="Clash_of_Titans.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Clash of Titans'? This action cannot be undone." class="w-full" method="post" action="/songs/aaa7c137-5dcb-428d-9114-fac7501e5538"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="xF6DoAp2RNn_G4WiNj_0CNAHenS5vw4cpg8EVGunZueVUZwDzhcxzYw6zr4RWe6eTJBSRiBbP7_FZUAq-6NKMw" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_4d79b02a-7f09-449a-b2b9-a90ada71ce9a">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/4d79b02a-7f09-449a-b2b9-a90ada71ce9a">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/4d79b02a-7f09-449a-b2b9-a90ada71ce9a/cover.jpeg" alt="Whispering Pines cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_4d79b02a-7f09-449a-b2b9-a90ada71ce9a">
  Whispering Pines
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
3:41
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_4d79b02a-7f09-449a-b2b9-a90ada71ce9a">
<form class="button_to" method="post" action="/songs/4d79b02a-7f09-449a-b2b9-a90ada71ce9a/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-gray-400 pointer-coarse:opacity-100 pointer-fine:opacity-0 pointer-fine:group-hover:opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"></path>
</svg>

<span class="sr-only">
Like
</span>
</button><input type="hidden" name="authenticity_token" value="mUtmo0XLlD7n3W-mfEL2VxAYkW7ctaueT7iP7VrCUFZmFYNKQtkXsyIXt1FUUoNt1K5sw6Fp0Tq4migkBXaasA" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/4d79b02a-7f09-449a-b2b9-a90ada71ce9a">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_4d79b02a-7f09-449a-b2b9-a90ada71ce9a" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_4d79b02a-7f09-449a-b2b9-a90ada71ce9a" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 882px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/4d79b02a-7f09-449a-b2b9-a90ada71ce9a" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/4d79b02a-7f09-449a-b2b9-a90ada71ce9a/audio.mp3" target="_blank" download="Whispering_Pines.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Whispering Pines'? This action cannot be undone." class="w-full" method="post" action="/songs/4d79b02a-7f09-449a-b2b9-a90ada71ce9a"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="iZAgLyrCgZ7t2_nMPf4pU_xV_D979HDh0l3akrb_eXIbOtcCzq_e_U7ufcisLjBkGOgND1EsfhyrtAuweskuTQ" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
<!-- BEGIN app/views/songs/_song_list_item.html.erb --><turbo-frame id="song_list_song_7cb90cf3-92af-4976-b4c3-c52974f09757">
<li class="@container group py-3 px-4">
<div class="flex items-center gap-4">
<!-- Cover Image -->
<div class="shrink-0">
<!-- Playable Song - Cover as Button -->
<a data-turbo-frame="song_player" class="relative w-12 h-12 rounded-lg overflow-hidden group/cover focus:ring-4 focus:outline-none focus:ring-blue-300 block" href="/songs/7cb90cf3-92af-4976-b4c3-c52974f09757">
  <img class="w-full h-full object-cover" src="https://cdn.musicify.me/songs/2/7cb90cf3-92af-4976-b4c3-c52974f09757/cover.jpeg" alt="Whispering Pines cover">

<!-- Play Icon Overlay - Enhanced for mobile -->
<div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-5 text-white">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18V6l8 6-8 6Z"></path>
</svg>

</div>
</a>      </div>
<!-- Song Info -->
<div class="flex-1 min-w-0">
<div class="flex items-center gap-2">
<p class="text-sm font-medium text-gray-900 dark:text-white truncate">
<turbo-frame class="contents" id="list_title_song_7cb90cf3-92af-4976-b4c3-c52974f09757">
  Whispering Pines
</turbo-frame>          </p>
</div>
<div class="flex items-center gap-2">
<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
3:37
</p>
<!-- In Progress Indicator -->
</div>
</div>
<!-- Actions -->
<div class="inline-flex items-center gap-1">
<!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
<!-- BEGIN app/views/songs/_list_favorite_button.html.erb --><turbo-frame id="list_favorite_button_song_7cb90cf3-92af-4976-b4c3-c52974f09757">
<form class="button_to" method="post" action="/songs/7cb90cf3-92af-4976-b4c3-c52974f09757/toggle_favorite"><input type="hidden" name="_method" value="patch" autocomplete="off"><button class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center rounded-md hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all text-red-600 opacity-100" type="submit">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="size-3.5">
<path d="m12.75 20.66 6.184-7.098c2.677-2.884 2.559-6.506.754-8.705-.898-1.095-2.206-1.816-3.72-1.855-1.293-.034-2.652.43-3.963 1.442-1.315-1.012-2.678-1.476-3.973-1.442-1.515.04-2.825.76-3.724 1.855-1.806 2.201-1.915 5.823.772 8.706l6.183 7.097c.19.216.46.34.743.34a.985.985 0 0 0 .743-.34Z"></path>
</svg>

<span class="sr-only">
Liked
</span>
</button><input type="hidden" name="authenticity_token" value="n7dfHMnjjOz2NTtw6_Kzj3IGyZXCTBKHsgBj9ryOKpKC4-WNpFmKfJYY_Cxr0qk3c37XksM7ol2PBrKfLZlLmw" autocomplete="off"></form></turbo-frame><!-- END app/views/songs/_list_favorite_button.html.erb -->
<!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
<a data-turbo-frame="song_details" class="hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" href="/songs/7cb90cf3-92af-4976-b4c3-c52974f09757">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

</a>        <!-- More Actions Dropdown -->
<div class="relative">
<button type="button" class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all" data-dropdown-toggle="actions_dropdown_song_7cb90cf3-92af-4976-b4c3-c52974f09757" data-dropdown-placement="left-start" aria-expanded="false">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-3.5">
<path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M12 6h.01M12 12h.01M12 18h.01"></path>
</svg>

<span class="sr-only">More actions</span>
</button>
<!-- Dropdown Menu -->
<div id="actions_dropdown_song_7cb90cf3-92af-4976-b4c3-c52974f09757" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" data-popper-placement="left-start" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-555.5px, 954px, 0px);">
<ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
  <!-- View Details - Only show in dropdown when hidden in main actions -->
  <li class="@sm:hidden">
    <a href="/songs/7cb90cf3-92af-4976-b4c3-c52974f09757" data-turbo-frame="song_details" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H8m12 0-4 4m4-4-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2"></path>
</svg>

      View Details
    </a>
  </li>
    <li>
      <a href="https://cdn.musicify.me/songs/2/7cb90cf3-92af-4976-b4c3-c52974f09757/audio.mp3" target="_blank" download="Whispering_Pines.mp3" class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"></path>
</svg>

        Download
      </a>
    </li>
</ul>
<div class="py-2">
  <form data-turbo-confirm="Are you sure you want to delete 'Whispering Pines'? This action cannot be undone." class="w-full" method="post" action="/songs/7cb90cf3-92af-4976-b4c3-c52974f09757"><input type="hidden" name="_method" value="delete" autocomplete="off"><button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400" title="Delete song" type="submit">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-4 mr-2">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"></path>
</svg>

    Delete
</button><input type="hidden" name="authenticity_token" value="9rTbcE-Zpj5R_gxKTFpUlwCLRGdajQLFRr7HzSzWNCHKaBz6jkBTZ5EAEvVlpOjAfxey9vIx3dVs0wLhRRsAmg" autocomplete="off"></form>            </div>
</div>
</div>
</div>
</div>
</li>
</turbo-frame><!-- END app/views/songs/_song_list_item.html.erb -->
</ul>
<!-- 搜索无结果状态 -->
<div class="p-6 text-center flex-1 flex peer-has-[li]:hidden flex-col justify-center">
<div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" class="size-8 text-gray-400">
<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18c0 1.1046-.89543 2-2 2s-2-.8954-2-2 .89543-2 2-2 2 .8954 2 2Zm0 0V6.33333L18 4v11.6667M8 10.3333 18 8m0 8c0 1.1046-.8954 2-2 2s-2-.8954-2-2 .8954-2 2-2 2 .8954 2 2Z"></path>
</svg>

</div>
<h3 class="text-lg font-medium text-gray-900 mb-2">No songs yet</h3>
<p class="text-gray-500">Start creating your first AI-generated song using the generation form</p>
</div>
<!-- END app/views/songs/_song_list.html.erb -->
</div>